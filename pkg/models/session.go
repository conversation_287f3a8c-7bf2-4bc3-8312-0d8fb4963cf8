package models

import "time"

// StreamSession represents a video stream session
type StreamSession struct {
	SessionID   string    `json:"session_id"`
	GBID        string    `json:"gb_id"`
	SSRC        string    `json:"ssrc"`
	DialogID    string    `json:"dialog_id"`
	Destination string    `json:"destination"` // "ip:port"
	StartTime   time.Time `json:"start_time"`
	Status      string    `json:"status"` // "requesting", "active", "closed"
}
