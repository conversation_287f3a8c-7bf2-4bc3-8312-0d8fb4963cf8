package models

// Device represents a camera device
type Device struct {
	GBID       string `json:"gb_id" xml:"DeviceID"`
	Name       string `json:"name" xml:"Name"`
	Status     string `json:"status" xml:"Status"`
	IP         string `json:"ip,omitempty" xml:"IPAddress,omitempty"`
	PlatformID string `json:"platform_id"`
}

// DeviceList represents the response from catalog query
type DeviceList struct {
	Devices []Device `json:"devices"`
	Total   int      `json:"total"`
}
