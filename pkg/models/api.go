package models

// APIResponse represents standard API response
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// StreamRequest represents stream request payload
type StreamRequest struct {
	GBID        string `json:"gb_id" binding:"required"`
	ReceiveIP   string `json:"receive_ip" binding:"required"`
	ReceivePort int    `json:"receive_port" binding:"required"`
}

// StreamResponse represents stream request response data
type StreamResponse struct {
	SSRC      string `json:"ssrc"`
	SessionID string `json:"session_id"`
}

// PTZRequest represents PTZ control request payload
type PTZRequest struct {
	GBID    string `json:"gb_id" binding:"required"`
	Command string `json:"command" binding:"required"`
	Speed   int    `json:"speed" binding:"min=0,max=255"`
}

// HealthResponse represents health check response
type HealthResponse struct {
	Status    string `json:"status"`
	Timestamp int64  `json:"timestamp"`
}
