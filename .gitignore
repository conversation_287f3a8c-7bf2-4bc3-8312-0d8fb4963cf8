# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build directory
build/
dist/
release/

# Configuration files (keep example)
config.yaml
config.yml
*.local.yaml
*.local.yml

# Log files
*.log
logs/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/

# Air (hot reload) temporary files
tmp/

# Test coverage
coverage.out
coverage.html

# Documentation build
docs/docs.go
docs/swagger.json
docs/swagger.yaml

# Docker
.dockerignore

# Environment files
.env
.env.local
.env.*.local
