package sip

import (
	"context"
	"fmt"
	"log/slog"
	"strconv"
	"strings"
	"time"

	"gb-gateway/internal/config"
	"gb-gateway/internal/state"
	"gb-gateway/pkg/models"

	"github.com/emiago/sipgo"
	"github.com/emiago/sipgo/sip"
	"github.com/google/uuid"
)

// Server represents SIP server
type Server struct {
	config       *config.ServerConfig
	stateManager *state.Manager
	ua           *sipgo.UserAgent
	server       *sipgo.Server
	client       *sipgo.Client
	logger       *slog.Logger
}

// NewServer creates a new SIP server
func NewServer(cfg *config.ServerConfig, stateManager *state.Manager) *Server {
	return &Server{
		config:       cfg,
		stateManager: stateManager,
		logger:       slog.Default(),
	}
}

// Start starts the SIP server
func (s *Server) Start(ctx context.Context) error {
	// Create User Agent
	ua, err := sipgo.NewUA()
	if err != nil {
		return fmt.Errorf("failed to create user agent: %w", err)
	}
	s.ua = ua

	// Create server and client
	server, err := sipgo.NewServer(ua)
	if err != nil {
		return fmt.Errorf("failed to create server: %w", err)
	}
	s.server = server

	client, err := sipgo.NewClient(ua)
	if err != nil {
		return fmt.Errorf("failed to create client: %w", err)
	}
	s.client = client

	// Register handlers
	s.server.OnRegister(s.handleRegister)
	s.server.OnMessage(s.handleMessage)
	s.server.OnInvite(s.handleInvite)
	s.server.OnBye(s.handleBye)

	// Start listening
	listenAddr := fmt.Sprintf("%s:%d", s.config.SIPIP, s.config.SIPPort)
	go func() {
		if err := s.server.ListenAndServe(ctx, "udp", listenAddr); err != nil {
			s.logger.Error("SIP server error", "error", err)
		}
	}()

	s.logger.Info("SIP server started", "address", listenAddr)
	return nil
}

// Stop stops the SIP server
func (s *Server) Stop() {
	if s.ua != nil {
		s.ua.Close()
	}
}

// handleRegister handles REGISTER requests
func (s *Server) handleRegister(req *sip.Request, tx sip.ServerTransaction) {
	// Extract platform information
	fromHeader := req.From()
	if fromHeader == nil {
		s.sendResponse(tx, req, 400, "Bad Request")
		return
	}

	platformID := fromHeader.Address.User
	sipURI := fromHeader.Address.String()

	// Extract expires
	expires := 3600 // default
	if expiresHeader := req.GetHeader("Expires"); expiresHeader != nil {
		if exp, err := strconv.Atoi(expiresHeader.Value()); err == nil {
			expires = exp
		}
	}

	// Extract IP and port from Via header
	viaHeader := req.Via()
	var ip string
	var port int
	if viaHeader != nil {
		ip = viaHeader.Host
		if viaHeader.Port != 0 {
			port = viaHeader.Port
		} else {
			port = 5060
		}
	}

	// Register platform
	platform := &models.Platform{
		ID:      platformID,
		SIPURI:  sipURI,
		Expires: expires,
		IP:      ip,
		Port:    port,
	}
	s.stateManager.RegisterPlatform(platform)

	// Send 200 OK response
	s.sendResponse(tx, req, 200, "OK")
	s.logger.Info("Platform registered", "id", platformID, "uri", sipURI)
}

// handleMessage handles MESSAGE requests
func (s *Server) handleMessage(req *sip.Request, tx sip.ServerTransaction) {
	// Send 200 OK first
	s.sendResponse(tx, req, 200, "OK")

	// Parse message body
	body := string(req.Body())
	if body == "" {
		return
	}

	s.logger.Debug("Received MESSAGE", "body", body)

	// Handle different message types based on content
	if strings.Contains(body, "Keepalive") {
		s.handleKeepalive(req)
	} else if strings.Contains(body, "Catalog") {
		s.handleCatalogResponse(req, body)
	}
}

// handleKeepalive handles keepalive messages
func (s *Server) handleKeepalive(req *sip.Request) {
	fromHeader := req.From()
	if fromHeader != nil {
		platformID := fromHeader.Address.User
		s.stateManager.UpdatePlatformLastSeen(platformID)
		s.logger.Debug("Keepalive received", "platform_id", platformID)
	}
}

// handleCatalogResponse handles catalog response messages
func (s *Server) handleCatalogResponse(req *sip.Request, body string) {
	// This is a simplified implementation
	// In a real implementation, you would parse the XML body
	// and extract device information
	s.logger.Debug("Catalog response received", "body", body)

	// TODO: Parse XML and extract devices
	// For now, return empty device list
	devices := []models.Device{}

	// Extract SN from body to match with pending request
	// This is simplified - you would parse XML properly
	sn := "default"

	if err := s.stateManager.SendCatalogResponse(sn, devices); err != nil {
		s.logger.Error("Failed to send catalog response", "error", err)
	}
}

// handleInvite handles INVITE requests
func (s *Server) handleInvite(req *sip.Request, tx sip.ServerTransaction) {
	// Send 200 OK response
	s.sendResponse(tx, req, 200, "OK")
	s.logger.Info("INVITE request handled")
}

// handleBye handles BYE requests
func (s *Server) handleBye(req *sip.Request, tx sip.ServerTransaction) {
	// Send 200 OK response
	s.sendResponse(tx, req, 200, "OK")
	s.logger.Info("BYE request handled")
}

// sendResponse sends a SIP response
func (s *Server) sendResponse(tx sip.ServerTransaction, req *sip.Request, statusCode int, reasonPhrase string) {
	response := sip.NewResponseFromRequest(req, statusCode, reasonPhrase, nil)

	if err := tx.Respond(response); err != nil {
		s.logger.Error("Failed to send SIP response", "error", err)
	}
}

// SendCatalogQuery sends a catalog query to platform
func (s *Server) SendCatalogQuery(platformID string) (string, error) {
	_, exists := s.stateManager.GetPlatform(platformID)
	if !exists {
		return "", fmt.Errorf("platform %s not found", platformID)
	}

	// Generate unique SN
	sn := uuid.New().String()[:8]

	// TODO: Implement SIP MESSAGE request for catalog query
	// This is a placeholder implementation
	s.logger.Info("Catalog query requested", "platform_id", platformID, "sn", sn)

	// For now, return empty device list after a short delay
	go func() {
		time.Sleep(1 * time.Second)
		devices := []models.Device{} // Empty list for now
		s.stateManager.SendCatalogResponse(sn, devices)
	}()

	return sn, nil
}

// SendInvite sends an INVITE request for video stream
func (s *Server) SendInvite(gbID, receiveIP string, receivePort int) (*models.StreamSession, error) {
	// Check if device exists
	_, exists := s.stateManager.GetDevice(gbID)
	if !exists {
		return nil, fmt.Errorf("device not found")
	}

	// Generate session info
	sessionID := uuid.New().String()
	ssrc := fmt.Sprintf("%d", time.Now().Unix()%1000000)

	// TODO: Implement SIP INVITE request
	// This is a placeholder implementation
	s.logger.Info("INVITE requested", "gb_id", gbID, "receive_ip", receiveIP, "receive_port", receivePort)

	// Create session
	session := &models.StreamSession{
		SessionID:   sessionID,
		GBID:        gbID,
		SSRC:        ssrc,
		Destination: fmt.Sprintf("%s:%d", receiveIP, receivePort),
	}

	s.stateManager.CreateSession(session)
	return session, nil
}

// SendPTZControl sends PTZ control command
func (s *Server) SendPTZControl(gbID, command string, speed int) error {
	// Check if device exists
	_, exists := s.stateManager.GetDevice(gbID)
	if !exists {
		return fmt.Errorf("device not found")
	}

	// TODO: Implement SIP MESSAGE request for PTZ control
	// This is a placeholder implementation
	s.logger.Info("PTZ control requested", "gb_id", gbID, "command", command, "speed", speed)

	return nil
}
