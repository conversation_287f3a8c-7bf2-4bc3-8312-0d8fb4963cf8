package http

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"time"

	"gb-gateway/internal/config"
	"gb-gateway/internal/core"
	"gb-gateway/pkg/models"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// Server represents HTTP server
type Server struct {
	config     *config.Config
	coreLogic  *core.Logic
	engine     *gin.Engine
	httpServer *http.Server
	logger     *slog.Logger
}

// NewServer creates a new HTTP server
func NewServer(cfg *config.Config, coreLogic *core.Logic) *Server {
	// Set gin mode based on log level
	if cfg.Log.IsDebugMode() {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	engine := gin.New()
	engine.Use(gin.Logger(), gin.Recovery())

	return &Server{
		config:    cfg,
		coreLogic: coreLogic,
		engine:    engine,
		logger:    slog.Default(),
	}
}

// Start starts the HTTP server
func (s *Server) Start(ctx context.Context) error {
	s.setupRoutes()

	addr := fmt.Sprintf(":%d", s.config.Server.HTTPPort)
	s.httpServer = &http.Server{
		Addr:    addr,
		Handler: s.engine,
	}

	go func() {
		if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.logger.Error("HTTP server error", "error", err)
		}
	}()

	s.logger.Info("HTTP server started", "address", addr)
	return nil
}

// Stop stops the HTTP server
func (s *Server) Stop(ctx context.Context) error {
	if s.httpServer != nil {
		return s.httpServer.Shutdown(ctx)
	}
	return nil
}

// setupRoutes sets up HTTP routes
func (s *Server) setupRoutes() {
	// Health check
	s.engine.GET("/health", s.healthCheck)

	// API routes
	api := s.engine.Group("/api/v1")
	{
		api.GET("/devices", s.getDevices)
		api.POST("/stream/request", s.requestStream)
		api.POST("/control/ptz", s.controlPTZ)
	}

	// Swagger documentation (only in debug mode)
	if s.config.Log.IsDebugMode() {
		s.engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	}
}

// @Summary Health check
// @Description Check if the service is running
// @Tags health
// @Produce json
// @Success 200 {object} models.HealthResponse
// @Router /health [get]
func (s *Server) healthCheck(c *gin.Context) {
	response := models.HealthResponse{
		Status:    "ok",
		Timestamp: time.Now().Unix(),
	}
	c.JSON(http.StatusOK, response)
}

// @Summary Get device list
// @Description Get list of all registered devices
// @Tags devices
// @Produce json
// @Param platform_id query string false "Platform ID filter"
// @Success 200 {object} models.APIResponse{data=[]models.Device}
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/devices [get]
func (s *Server) getDevices(c *gin.Context) {
	platformID := c.Query("platform_id")

	devices, err := s.coreLogic.GetDevices(platformID)
	if err != nil {
		s.logger.Error("Failed to get devices", "error", err)
		c.JSON(http.StatusInternalServerError, models.APIResponse{
			Code:    5001,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    0,
		Message: "success",
		Data:    devices,
	})
}

// @Summary Request video stream
// @Description Request video stream from a device
// @Tags stream
// @Accept json
// @Produce json
// @Param request body models.StreamRequest true "Stream request"
// @Success 200 {object} models.APIResponse{data=models.StreamResponse}
// @Failure 400 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/stream/request [post]
func (s *Server) requestStream(c *gin.Context) {
	var req models.StreamRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    4000,
			Message: "Invalid request format",
		})
		return
	}

	response, err := s.coreLogic.RequestStream(req.GBID, req.ReceiveIP, req.ReceivePort)
	if err != nil {
		s.logger.Error("Failed to request stream", "error", err)
		statusCode := http.StatusInternalServerError
		errorCode := 5001
		if err.Error() == "device not found" {
			statusCode = http.StatusNotFound
			errorCode = 4004
		}

		c.JSON(statusCode, models.APIResponse{
			Code:    errorCode,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    0,
		Message: "stream request sent successfully",
		Data:    response,
	})
}

// @Summary PTZ control
// @Description Control PTZ (Pan-Tilt-Zoom) of a device
// @Tags control
// @Accept json
// @Produce json
// @Param request body models.PTZRequest true "PTZ control request"
// @Success 200 {object} models.APIResponse
// @Failure 400 {object} models.APIResponse
// @Failure 404 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/control/ptz [post]
func (s *Server) controlPTZ(c *gin.Context) {
	var req models.PTZRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:    4000,
			Message: "Invalid request format",
		})
		return
	}

	err := s.coreLogic.ControlPTZ(req.GBID, req.Command, req.Speed)
	if err != nil {
		s.logger.Error("Failed to control PTZ", "error", err)
		statusCode := http.StatusInternalServerError
		errorCode := 5001
		if err.Error() == "device not found" {
			statusCode = http.StatusNotFound
			errorCode = 4004
		}

		c.JSON(statusCode, models.APIResponse{
			Code:    errorCode,
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:    0,
		Message: "ptz command sent successfully",
	})
}
