package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"log/slog"
	"os"
	"os/signal"
	"syscall"
	"time"

	"gb-gateway/internal/config"
	"gb-gateway/internal/core"
	"gb-gateway/internal/http"
	"gb-gateway/internal/sip"
	"gb-gateway/internal/state"
)

// @title GB-Gateway API
// @version 1.0
// @description GB/T 28181 协议网关服务，提供HTTP API到SIP协议的转换
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name BSD-2-Clause
// @license.url https://opensource.org/licenses/BSD-2-Clause

// @host localhost:8080
// @BasePath /
func main() {
	var configPath string
	flag.StringVar(&configPath, "config", "config.yaml", "Path to configuration file")
	flag.Parse()

	// Load configuration
	cfg, err := config.Load(configPath)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Setup logger
	if err := cfg.Log.SetupLogger(); err != nil {
		log.Fatalf("Failed to setup logger: %v", err)
	}

	slog.Info("Starting GB-Gateway", "version", "1.0.0")

	// Create state manager
	stateManager := state.NewManager()

	// Create SIP server
	sipServer := sip.NewServer(&cfg.Server, stateManager)

	// Create core logic
	coreLogic := core.NewLogic(stateManager, sipServer)

	// Create HTTP server
	httpServer := http.NewServer(cfg, coreLogic)

	// Create context for graceful shutdown
	ctx, cancel := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer cancel()

	// Start SIP server
	if err := sipServer.Start(ctx); err != nil {
		slog.Error("Failed to start SIP server", "error", err)
		os.Exit(1)
	}

	// Start HTTP server
	if err := httpServer.Start(ctx); err != nil {
		slog.Error("Failed to start HTTP server", "error", err)
		os.Exit(1)
	}

	slog.Info("GB-Gateway started successfully")
	slog.Info("HTTP API available", "url", fmt.Sprintf("http://localhost:%d", cfg.Server.HTTPPort))
	if cfg.Log.IsDebugMode() {
		slog.Info("Swagger UI available", "url", fmt.Sprintf("http://localhost:%d/swagger/index.html", cfg.Server.HTTPPort))
	}

	// Wait for shutdown signal
	<-ctx.Done()
	slog.Info("Shutting down GB-Gateway...")

	// Create shutdown context with timeout
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// Stop HTTP server
	if err := httpServer.Stop(shutdownCtx); err != nil {
		slog.Error("Failed to stop HTTP server", "error", err)
	}

	// Stop SIP server
	sipServer.Stop()

	slog.Info("GB-Gateway stopped")
}
