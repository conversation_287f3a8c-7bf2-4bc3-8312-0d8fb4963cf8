#!/bin/bash

# GB-Gateway Test Script
# This script tests the main functionality of GB-Gateway

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="http://localhost:8080"
API_BASE="$BASE_URL/api/v1"

# Test data
TEST_GB_ID="34020000001320000001"
TEST_RECEIVE_IP="*************"
TEST_RECEIVE_PORT=15000

echo -e "${YELLOW}=== GB-Gateway Test Suite ===${NC}"
echo "Testing GB-Gateway functionality..."
echo

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓ $2${NC}"
    else
        echo -e "${RED}✗ $2${NC}"
        return 1
    fi
}

# Function to test HTTP endpoint
test_endpoint() {
    local method=$1
    local url=$2
    local data=$3
    local expected_status=$4
    local description=$5

    echo -n "Testing $description... "
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url")
    else
        response=$(curl -s -w "%{http_code}" -X "$method" "$url")
    fi
    
    status_code="${response: -3}"
    body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✓${NC}"
        if [ -n "$body" ] && [ "$body" != "null" ]; then
            echo "  Response: $body" | head -c 200
            echo
        fi
        return 0
    else
        echo -e "${RED}✗ (Expected $expected_status, got $status_code)${NC}"
        echo "  Response: $body"
        return 1
    fi
}

# Wait for server to start
echo "Waiting for server to start..."
for i in {1..30}; do
    if curl -s "$BASE_URL/health" > /dev/null 2>&1; then
        echo -e "${GREEN}Server is ready!${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${RED}Server failed to start within 30 seconds${NC}"
        exit 1
    fi
    sleep 1
done

echo

# Test 1: Health Check
echo "=== Test 1: Health Check ==="
test_endpoint "GET" "$BASE_URL/health" "" "200" "Health check endpoint"
echo

# Test 2: Get Devices (should work even without registered platforms)
echo "=== Test 2: Device List ==="
test_endpoint "GET" "$API_BASE/devices" "" "500" "Get devices (no platforms registered)"
echo

# Test 3: Stream Request (should fail - device not found)
echo "=== Test 3: Stream Request ==="
stream_request='{
    "gb_id": "'$TEST_GB_ID'",
    "receive_ip": "'$TEST_RECEIVE_IP'",
    "receive_port": '$TEST_RECEIVE_PORT'
}'
test_endpoint "POST" "$API_BASE/stream/request" "$stream_request" "404" "Request video stream (device not found)"
echo

# Test 4: PTZ Control (should fail - device not found)
echo "=== Test 4: PTZ Control ==="
ptz_request='{
    "gb_id": "'$TEST_GB_ID'",
    "command": "left",
    "speed": 100
}'
test_endpoint "POST" "$API_BASE/control/ptz" "$ptz_request" "404" "PTZ control (device not found)"
echo

# Test 5: Invalid PTZ Command
echo "=== Test 5: Invalid PTZ Command ==="
invalid_ptz_request='{
    "gb_id": "'$TEST_GB_ID'",
    "command": "invalid_command",
    "speed": 100
}'
test_endpoint "POST" "$API_BASE/control/ptz" "$invalid_ptz_request" "400" "Invalid PTZ command"
echo

# Test 6: Invalid Stream Request
echo "=== Test 6: Invalid Stream Request ==="
invalid_stream_request='{
    "gb_id": "",
    "receive_ip": "'$TEST_RECEIVE_IP'",
    "receive_port": '$TEST_RECEIVE_PORT'
}'
test_endpoint "POST" "$API_BASE/stream/request" "$invalid_stream_request" "400" "Invalid stream request"
echo

# Test 7: Swagger UI (only in debug mode)
echo "=== Test 7: Swagger UI ==="
test_endpoint "GET" "$BASE_URL/swagger/index.html" "" "200" "Swagger UI access"
echo

# Test 8: API Documentation JSON
echo "=== Test 8: API Documentation ==="
test_endpoint "GET" "$BASE_URL/swagger/doc.json" "" "200" "Swagger JSON documentation"
echo

echo -e "${YELLOW}=== Test Summary ===${NC}"
echo "Basic API functionality tests completed."
echo
echo -e "${YELLOW}Note:${NC} Some tests are expected to fail due to no registered SIP platforms."
echo "This is normal behavior when testing without actual GB28181 devices."
echo
echo -e "${GREEN}✓ HTTP server is working correctly${NC}"
echo -e "${GREEN}✓ API endpoints are responding${NC}"
echo -e "${GREEN}✓ Error handling is working${NC}"
echo -e "${GREEN}✓ Swagger documentation is accessible${NC}"
echo
echo "To test with real devices:"
echo "1. Configure a GB28181 platform to register with this gateway"
echo "2. Set the platform's SIP server to point to this gateway (port 5060)"
echo "3. Run the device tests again"
echo
echo -e "${YELLOW}Test completed!${NC}"
